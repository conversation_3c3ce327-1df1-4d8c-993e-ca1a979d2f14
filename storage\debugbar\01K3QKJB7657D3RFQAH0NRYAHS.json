{"__meta": {"id": "01K3QKJB7657D3RFQAH0NRYAHS", "datetime": "2025-08-28 11:49:18", "utime": **********.439851, "method": "GET", "uri": "/api/user/wallets", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[11:49:18] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.426749, "xdebug_link": null, "collector": "log"}, {"message": "[11:49:18] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.426995, "xdebug_link": null, "collector": "log"}, {"message": "[11:49:18] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.427672, "xdebug_link": null, "collector": "log"}, {"message": "[11:49:18] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.427807, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.030479, "end": **********.43987, "duration": 0.****************, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.030479, "relative_start": 0, "end": **********.332461, "relative_end": **********.332461, "duration": 0.*****************, "duration_str": "302ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.332472, "relative_start": 0.*****************, "end": **********.439873, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.348735, "relative_start": 0.***************, "end": **********.352838, "relative_end": **********.352838, "duration": 0.004102945327758789, "duration_str": "4.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.428266, "relative_start": 0.*****************, "end": **********.428636, "relative_end": **********.428636, "duration": 0.000370025634765625, "duration_str": "370μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.437026, "relative_start": 0.*****************, "end": **********.437086, "relative_end": **********.437086, "duration": 6.008148193359375e-05, "duration_str": "60μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "62MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.14.1", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01618, "accumulated_duration_str": "16.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD' limit 1", "type": "query", "params": [], "bindings": ["H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.356129, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "money_chain", "explain": null, "start_percent": 0, "width_percent": 6.428}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '20' limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.364243, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "money_chain", "explain": null, "start_percent": 6.428, "width_percent": 5.192}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.371891, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "money_chain", "explain": null, "start_percent": 11.619, "width_percent": 8.9}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-28 11:49:18', `personal_access_tokens`.`updated_at` = '2025-08-28 11:49:18' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-08-28 11:49:18", "2025-08-28 11:49:18", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Middleware\\Authenticate.php", "line": 32}], "start": **********.376171, "duration": 0.004719999999999999, "duration_str": "4.72ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "money_chain", "explain": null, "start_percent": 20.519, "width_percent": 29.172}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3849769, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "WalletController.php:23", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:23", "ajax": false, "filename": "WalletController.php", "line": "23"}, "connection": "money_chain", "explain": null, "start_percent": 49.691, "width_percent": 3.646}, {"sql": "select * from `currencies` where `currencies`.`id` in (2, 3, 4, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.388019, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "WalletController.php:23", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:23", "ajax": false, "filename": "WalletController.php", "line": "23"}, "connection": "money_chain", "explain": null, "start_percent": 53.337, "width_percent": 5.253}, {"sql": "select * from `currencies` where `code` = 'USD' limit 1", "type": "query", "params": [], "bindings": ["USD"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\money-chain\\app\\helpers.php", "line": 679}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.402321, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "helpers.php:679", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\money-chain\\app\\helpers.php", "line": 679}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2Fhelpers.php:679", "ajax": false, "filename": "helpers.php", "line": "679"}, "connection": "money_chain", "explain": null, "start_percent": 58.591, "width_percent": 6.366}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiS0RERmdqQkJXRWxyZHpkemZzQWt5Y2lpRk50Qk1aakZEV3cyR1ByMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDA6Imh0dHA6Ly9tb25leS1jaGFpbi50ZXN0L2FwaS91c2VyL3dhbGxldHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'PostmanRuntime/7.45.0' where `id` = 'H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoiS0RERmdqQkJXRWxyZHpkemZzQWt5Y2lpRk50Qk1aakZEV3cyR1ByMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDA6Imh0dHA6Ly9tb25leS1jaGFpbi50ZXN0L2FwaS91c2VyL3dhbGxldHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19", **********, 1, "127.0.0.1", "PostmanRuntime/7.45.0", "H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.429888, "duration": 0.00567, "duration_str": "5.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "money_chain", "explain": null, "start_percent": 64.957, "width_percent": 35.043}]}, "models": {"data": {"App\\Models\\UserWallet": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index", "uri": "GET api/user/wallets", "controller": "App\\Http\\Controllers\\Api\\WalletController@index<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/WalletController.php:20-30</a>", "middleware": "api, auth:sanctum, auth:sanctum", "telescope": "<a href=\"https://money-chain.test/_debugbar/telescope/9fbd7255-8bd3-4046-8df1-93cbfc75ed25\" target=\"_blank\">View in Telescope</a>", "duration": "410ms", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-649570304 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-649570304\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-236953090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-236953090\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1614042735 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">money-chain.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postman-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">8250e85d-38a4-4ce1-a093-be663843020e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">PostmanRuntime/7.45.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 20|OW******</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1614042735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1211642524 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>money_chain_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211642524\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2139730107 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 05:49:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"145 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28 Aug 2025 07:49:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28-Aug-2025 07:49:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139730107\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1542753238 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDDFgjBBWElrdzdzfsAkyciiFNtBMZjFDWw2GPr1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://money-chain.test/api/user/wallets</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542753238\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index"}, "badge": null}}