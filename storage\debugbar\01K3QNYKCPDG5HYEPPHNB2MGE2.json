{"__meta": {"id": "01K3QNYKCPDG5HYEPPHNB2MGE2", "datetime": "2025-08-28 12:30:57", "utime": **********.175064, "method": "GET", "uri": "/api/user/wallets?payment", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[12:30:56] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.949831, "xdebug_link": null, "collector": "log"}, {"message": "[12:30:56] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.950119, "xdebug_link": null, "collector": "log"}, {"message": "[12:30:57] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.060363, "xdebug_link": null, "collector": "log"}, {"message": "[12:30:57] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.060486, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.440423, "end": **********.175082, "duration": 0.**************, "duration_str": "735ms", "measures": [{"label": "Booting", "start": **********.440423, "relative_start": 0, "end": **********.777061, "relative_end": **********.777061, "duration": 0.****************, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.777074, "relative_start": 0.***************, "end": **********.175085, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.793855, "relative_start": 0.*****************, "end": **********.798588, "relative_end": **********.798588, "duration": 0.004733085632324219, "duration_str": "4.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.164378, "relative_start": 0.****************, "end": **********.164804, "relative_end": **********.164804, "duration": 0.0004260540008544922, "duration_str": "426μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.172413, "relative_start": 0.****************, "end": **********.172473, "relative_end": **********.172473, "duration": 5.984306335449219e-05, "duration_str": "60μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": *********, "peak_usage_str": "156MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.14.1", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01571, "accumulated_duration_str": "15.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD' limit 1", "type": "query", "params": [], "bindings": ["H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.801946, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "money_chain", "explain": null, "start_percent": 0, "width_percent": 3.501}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '20' limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.8099108, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "money_chain", "explain": null, "start_percent": 3.501, "width_percent": 7.257}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.817822, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "money_chain", "explain": null, "start_percent": 10.757, "width_percent": 9.102}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-28 12:30:56', `personal_access_tokens`.`updated_at` = '2025-08-28 12:30:56' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-08-28 12:30:56", "2025-08-28 12:30:56", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Middleware\\Authenticate.php", "line": 32}], "start": **********.822426, "duration": 0.00505, "duration_str": "5.05ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "money_chain", "explain": null, "start_percent": 19.86, "width_percent": 32.145}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.8320749, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:23", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:23", "ajax": false, "filename": "WalletController.php", "line": "23"}, "connection": "money_chain", "explain": null, "start_percent": 52.005, "width_percent": 6.811}, {"sql": "select * from `currencies` where `currencies`.`id` in (2, 3, 4, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.8354921, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:23", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 23}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:23", "ajax": false, "filename": "WalletController.php", "line": "23"}, "connection": "money_chain", "explain": null, "start_percent": 58.816, "width_percent": 7.32}, {"sql": "select * from `currencies` where `code` = 'USD' limit 1", "type": "query", "params": [], "bindings": ["USD"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\money-chain\\app\\helpers.php", "line": 679}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.850745, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "helpers.php:679", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\money-chain\\app\\helpers.php", "line": 679}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2Fhelpers.php:679", "ajax": false, "filename": "helpers.php", "line": "679"}, "connection": "money_chain", "explain": null, "start_percent": 66.136, "width_percent": 3.055}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiS0RERmdqQkJXRWxyZHpkemZzQWt5Y2lpRk50Qk1aakZEV3cyR1ByMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDk6Imh0dHA6Ly9tb25leS1jaGFpbi50ZXN0L2FwaS91c2VyL3dhbGxldHM/cGF5bWVudD0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'PostmanRuntime/7.45.0' where `id` = 'H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoiS0RERmdqQkJXRWxyZHpkemZzQWt5Y2lpRk50Qk1aakZEV3cyR1ByMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDk6Imh0dHA6Ly9tb25leS1jaGFpbi50ZXN0L2FwaS91c2VyL3dhbGxldHM/cGF5bWVudD0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19", **********, 1, "127.0.0.1", "PostmanRuntime/7.45.0", "H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.1660411, "duration": 0.00484, "duration_str": "4.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "money_chain", "explain": null, "start_percent": 69.192, "width_percent": 30.808}]}, "models": {"data": {"App\\Models\\UserWallet": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets?payment=", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index", "uri": "GET api/user/wallets", "controller": "App\\Http\\Controllers\\Api\\WalletController@index<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/WalletController.php:20-30</a>", "middleware": "api, auth:sanctum, auth:sanctum", "telescope": "<a href=\"https://money-chain.test/_debugbar/telescope/9fbd813a-5047-459b-9755-16a4dc0103d5\" target=\"_blank\">View in Telescope</a>", "duration": "735ms", "peak_memory": "160MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2125616059 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>payment</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125616059\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-265737189 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-265737189\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-551198211 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">money-chain.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postman-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">efa25ee4-0916-4876-ac3d-1c1a39165e6e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">PostmanRuntime/7.45.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 20|OW******</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551198211\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1565774077 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>money_chain_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565774077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1002891335 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 06:30:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"145 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28 Aug 2025 08:30:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28-Aug-2025 08:30:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002891335\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-714662449 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDDFgjBBWElrdzdzfsAkyciiFNtBMZjFDWw2GPr1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://money-chain.test/api/user/wallets?payment=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714662449\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets?payment=", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index"}, "badge": null}}