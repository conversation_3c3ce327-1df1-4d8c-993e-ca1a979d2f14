<?php

use App\Http\Controllers\Api\Payment\PaymentController;
use App\Http\Controllers\Api\Payment\SandboxPaymentController;
use Illuminate\Support\Facades\Route;

Route::middleware('merchant_system')->group(function () {
    // Real payment
    Route::post('/access-token', [PaymentController::class, 'getAccessToken']);
    Route::post('/make-payment', [PaymentController::class, 'makePayment']);

    // Sandbox payment
    Route::prefix('sandbox')->group(function () {
        Route::post('/access-token', [SandboxPaymentController::class, 'getAccessToken']);
        Route::post('/make-payment', [SandboxPaymentController::class, 'makePayment']);
    });
});
