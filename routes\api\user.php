<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\KycController;
use App\Http\Controllers\Api\GiftController;
use App\Http\Controllers\Api\TicketController;
use App\Http\Controllers\Api\WalletController;
use App\Http\Controllers\Api\CashoutController;
use App\Http\Controllers\Api\InvoiceController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\AddMoneyController;
use App\Http\Controllers\Api\ExchangeController;
use App\Http\Controllers\Api\ReferralController;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\WithdrawController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\RequestMoneyController;
use App\Http\Controllers\Api\TransferMoneyController;
use App\Http\Controllers\Api\WithdrawAccountController;

Route::middleware('auth:sanctum')->group(function () {
    // Dashboard
    Route::controller(DashboardController::class)->group(function () {
        Route::get('dashboard', 'dashboard');
        Route::get('mark-as-read-notification', 'markNotification');
        Route::get('qrcode', 'qrCode');
        Route::get('activity-chart', 'activityChartInfo');

        // Transactions
        Route::get('transactions', 'transactions');
        Route::get('transaction/{tnx?}', 'transactionDetail');
    });

    // Wallets
    Route::apiResource('wallets', WalletController::class)->only('index', 'store', 'destroy');

    // Add Money
    Route::apiResource('add-money', AddMoneyController::class)->only('index', 'store');

    // Payment
    Route::controller(PaymentController::class)->prefix('payment')->group(function () {
        Route::get('settings', 'index');
        Route::post('make', 'store');
        Route::get('history', 'history');
    });

    // Additional invoice routes
    Route::prefix('invoices')->group(function () {
        Route::get('config', [InvoiceController::class, 'config']);
    });

    // Invoice resource routes
    Route::apiResource('invoices', InvoiceController::class);

    // Request Money Routes
    Route::prefix('request-money')->group(function () {
        Route::get('config', [RequestMoneyController::class, 'config']);
        Route::post('/', [RequestMoneyController::class, 'store']);
        Route::get('history', [RequestMoneyController::class, 'history']);
        Route::get('received', [RequestMoneyController::class, 'receivedRequests']);
        Route::post('{id}/action', [RequestMoneyController::class, 'action']);
    });

    // Gift Routes
    Route::prefix('gifts')->group(function () {
        Route::get('config', [GiftController::class, 'config']);
        Route::post('/', [GiftController::class, 'store']);
        Route::post('redeem', [GiftController::class, 'redeem']);
        Route::get('history', [GiftController::class, 'history']);
        Route::get('redeem/history', [GiftController::class, 'redeemHistory']);
    });

    // Transfer Money Routes
    Route::prefix('transfer')->group(function () {
        Route::get('config', [TransferMoneyController::class, 'config']);
        Route::post('/', [TransferMoneyController::class, 'store']);
        Route::get('history', [TransferMoneyController::class, 'history']);
    });

    // Cashout Routes
    Route::prefix('cashout')->group(function () {
        Route::get('config', [CashoutController::class, 'config']);
        Route::post('/', [CashoutController::class, 'store']);
        Route::get('history', [CashoutController::class, 'history']);
    });

    // Withdraw Account Routes
    Route::prefix('withdraw-accounts')->group(function () {
        Route::get('config', [WithdrawAccountController::class, 'config']);
        Route::get('methods/list', [WithdrawAccountController::class, 'getWithdrawMethods']);
        Route::get('methods/{id}/details', [WithdrawAccountController::class, 'getMethodDetails']);
    });
    Route::apiResource('withdraw-accounts', WithdrawAccountController::class);

    // Withdraw Routes
    Route::post('withdraw', WithdrawController::class);

    // Exchange Routes
    Route::prefix('exchange')->group(function () {
        Route::get('config', [ExchangeController::class, 'config']);
        Route::post('/', [ExchangeController::class, 'store']);
        Route::get('history', [ExchangeController::class, 'history']);
    });

    // Referral
    Route::prefix('referral')->controller(ReferralController::class)->group(function () {
        Route::get('info', 'index');
        Route::get('direct', 'directReferrals');
        Route::get('tree', 'referralTree');
    });

    // Settings
    Route::prefix('settings')->controller(SettingsController::class)->group(function () {
        Route::post('profile', 'profileUpdate');
        Route::post('2fa/{type}', 'twoFa');
        Route::post('passcode/verify', 'verifyPasscode');
        Route::post('account-close', 'accountClose');
        Route::post('change-password', 'updatePassword');
    });

    // KYC
    Route::get('kyc/history', [KycController::class, 'histories']);
    Route::apiResource('kyc', KycController::class)->only('index', 'store');

    // Ticket
    Route::get('ticket/config', [TicketController::class, 'config']);
    Route::apiResource('ticket', TicketController::class)->except('update', 'destroy');
    Route::post('ticket/reply/{uuid}', [TicketController::class, 'reply']);
    Route::post('ticket/action/{uuid}', [TicketController::class, 'action']);

});
