{"__meta": {"id": "01K3QYSPJ34WD7PKDNRWXFB5MP", "datetime": "2025-08-28 15:05:33", "utime": **********.763884, "method": "GET", "uri": "/api/user/wallets?payment", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[15:05:33] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.655123, "xdebug_link": null, "collector": "log"}, {"message": "[15:05:33] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.65536, "xdebug_link": null, "collector": "log"}, {"message": "[15:05:33] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.707911, "xdebug_link": null, "collector": "log"}, {"message": "[15:05:33] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.708038, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.104588, "end": **********.763903, "duration": 0.****************, "duration_str": "659ms", "measures": [{"label": "Booting", "start": **********.104588, "relative_start": 0, "end": **********.517126, "relative_end": **********.517126, "duration": 0.****************, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.51714, "relative_start": 0.****************, "end": **********.763906, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.53497, "relative_start": 0.*****************, "end": **********.541108, "relative_end": **********.541108, "duration": 0.006137847900390625, "duration_str": "6.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.759783, "relative_start": 0.****************, "end": **********.760234, "relative_end": **********.760234, "duration": 0.00045108795166015625, "duration_str": "451μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.761218, "relative_start": 0.****************, "end": **********.761268, "relative_end": **********.761268, "duration": 4.982948303222656e-05, "duration_str": "50μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": *********, "peak_usage_str": "110MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.14.1", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01211, "accumulated_duration_str": "12.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '20' limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.549284, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "money_chain", "explain": null, "start_percent": 0, "width_percent": 24.195}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.560196, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "money_chain", "explain": null, "start_percent": 24.195, "width_percent": 20.562}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-28 15:05:33', `personal_access_tokens`.`updated_at` = '2025-08-28 15:05:33' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-08-28 15:05:33", "2025-08-28 15:05:33", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Middleware\\Authenticate.php", "line": 32}], "start": **********.565718, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "money_chain", "explain": null, "start_percent": 44.756, "width_percent": 36.746}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.574864, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "WalletController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:24", "ajax": false, "filename": "WalletController.php", "line": "24"}, "connection": "money_chain", "explain": null, "start_percent": 81.503, "width_percent": 7.432}, {"sql": "select * from `currencies` where `currencies`.`id` in (2, 3, 4, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.5782661, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:24", "ajax": false, "filename": "WalletController.php", "line": "24"}, "connection": "money_chain", "explain": null, "start_percent": 88.935, "width_percent": 11.065}]}, "models": {"data": {"App\\Models\\UserWallet": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets?payment=", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index", "uri": "GET api/user/wallets", "controller": "App\\Http\\Controllers\\Api\\WalletController@index<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/WalletController.php:21-29</a>", "middleware": "api, auth:sanctum, auth:sanctum", "telescope": "<a href=\"https://money-chain.test/_debugbar/telescope/9fbdb885-43ac-491a-9b68-dfb50fe3a1bd\" target=\"_blank\">View in Telescope</a>", "duration": "656ms", "peak_memory": "114MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-771843556 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>payment</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771843556\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1954147286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1954147286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1442491742 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">money-chain.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postman-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">6b68f0e9-1a19-4a88-b830-48f50911c1ea</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">PostmanRuntime/7.45.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 20|OW******</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442491742\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-943444892 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>money_chain_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943444892\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-553614642 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 09:05:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"145 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28 Aug 2025 11:05:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28-Aug-2025 11:05:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553614642\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1719742576 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16mhBB7JACIDVcdAs4EiKZWlIdLcdd5zuOriLhmu</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://money-chain.test/api/user/wallets?payment=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719742576\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets?payment=", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index"}, "badge": null}}