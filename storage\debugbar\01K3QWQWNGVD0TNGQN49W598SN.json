{"__meta": {"id": "01K3QWQWNGVD0TNGQN49W598SN", "datetime": "2025-08-28 14:29:37", "utime": **********.328693, "method": "GET", "uri": "/api/user/wallets?payment", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[14:29:37] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.218337, "xdebug_link": null, "collector": "log"}, {"message": "[14:29:37] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.218514, "xdebug_link": null, "collector": "log"}, {"message": "[14:29:37] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.26642, "xdebug_link": null, "collector": "log"}, {"message": "[14:29:37] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.266541, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.776712, "end": **********.32871, "duration": 0.****************, "duration_str": "552ms", "measures": [{"label": "Booting", "start": **********.776712, "relative_start": 0, "end": **********.093147, "relative_end": **********.093147, "duration": 0.****************, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.093156, "relative_start": 0.*****************, "end": **********.328712, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.108746, "relative_start": 0.****************, "end": **********.11254, "relative_end": **********.11254, "duration": 0.003793954849243164, "duration_str": "3.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.316419, "relative_start": 0.****************, "end": **********.316844, "relative_end": **********.316844, "duration": 0.00042510032653808594, "duration_str": "425μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.326319, "relative_start": 0.****************, "end": **********.326381, "relative_end": **********.326381, "duration": 6.198883056640625e-05, "duration_str": "62μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": *********, "peak_usage_str": "111MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.14.1", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.014869999999999998, "accumulated_duration_str": "14.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD' limit 1", "type": "query", "params": [], "bindings": ["H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.115692, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "money_chain", "explain": null, "start_percent": 0, "width_percent": 4.64}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '20' limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.123743, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "money_chain", "explain": null, "start_percent": 4.64, "width_percent": 4.976}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.1307878, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "money_chain", "explain": null, "start_percent": 9.617, "width_percent": 3.699}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-28 14:29:37', `personal_access_tokens`.`updated_at` = '2025-08-28 14:29:37' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-08-28 14:29:37", "2025-08-28 14:29:37", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Middleware\\Authenticate.php", "line": 32}], "start": **********.134135, "duration": 0.00533, "duration_str": "5.33ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "money_chain", "explain": null, "start_percent": 13.315, "width_percent": 35.844}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.143999, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "WalletController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:24", "ajax": false, "filename": "WalletController.php", "line": "24"}, "connection": "money_chain", "explain": null, "start_percent": 49.159, "width_percent": 3.564}, {"sql": "select * from `currencies` where `currencies`.`id` in (2, 3, 4, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.146779, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "WalletController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:24", "ajax": false, "filename": "WalletController.php", "line": "24"}, "connection": "money_chain", "explain": null, "start_percent": 52.724, "width_percent": 2.354}, {"sql": "update `sessions` set `payload` = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiS0RERmdqQkJXRWxyZHpkemZzQWt5Y2lpRk50Qk1aakZEV3cyR1ByMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDk6Imh0dHA6Ly9tb25leS1jaGFpbi50ZXN0L2FwaS91c2VyL3dhbGxldHM/cGF5bWVudD0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'PostmanRuntime/7.45.0' where `id` = 'H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoiS0RERmdqQkJXRWxyZHpkemZzQWt5Y2lpRk50Qk1aakZEV3cyR1ByMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDk6Imh0dHA6Ly9tb25leS1jaGFpbi50ZXN0L2FwaS91c2VyL3dhbGxldHM/cGF5bWVudD0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19", **********, 1, "127.0.0.1", "PostmanRuntime/7.45.0", "H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.31818, "duration": 0.006679999999999999, "duration_str": "6.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "money_chain", "explain": null, "start_percent": 55.077, "width_percent": 44.923}]}, "models": {"data": {"App\\Models\\UserWallet": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets?payment=", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index", "uri": "GET api/user/wallets", "controller": "App\\Http\\Controllers\\Api\\WalletController@index<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/WalletController.php:21-29</a>", "middleware": "api, auth:sanctum, auth:sanctum", "telescope": "<a href=\"https://money-chain.test/_debugbar/telescope/9fbdabaa-ce41-48a9-92b9-ed56af1a1fcb\" target=\"_blank\">View in Telescope</a>", "duration": "553ms", "peak_memory": "114MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-844316583 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>payment</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844316583\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-144999527 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-144999527\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-506159024 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">money-chain.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postman-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">4104ee95-8f6b-485a-ac2d-dd814c0b4d4d</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">PostmanRuntime/7.45.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 20|OW******</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506159024\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-283059384 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>money_chain_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283059384\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1091112502 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 08:29:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"145 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28 Aug 2025 10:29:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28-Aug-2025 10:29:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091112502\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1201477613 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDDFgjBBWElrdzdzfsAkyciiFNtBMZjFDWw2GPr1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://money-chain.test/api/user/wallets?payment=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1201477613\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets?payment=", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index"}, "badge": null}}