{"__meta": {"id": "01K3QX5EQDNBSAE6RP7WB93RKJ", "datetime": "2025-08-28 14:37:01", "utime": **********.807415, "method": "GET", "uri": "/api/user/wallets", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[14:37:01] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.795231, "xdebug_link": null, "collector": "log"}, {"message": "[14:37:01] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.79544, "xdebug_link": null, "collector": "log"}, {"message": "[14:37:01] LOG.warning: preg_match(): Passing null to parameter #2 ($subject) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 674", "message_html": null, "is_string": false, "label": "warning", "time": **********.799452, "xdebug_link": null, "collector": "log"}, {"message": "[14:37:01] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php on line 283", "message_html": null, "is_string": false, "label": "warning", "time": **********.799559, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.684024, "end": **********.807433, "duration": 1.****************, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": **********.684024, "relative_start": 0, "end": **********.575547, "relative_end": **********.575547, "duration": 0.****************, "duration_str": "892ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.575568, "relative_start": 0.****************, "end": **********.807435, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.69298, "relative_start": 1.***************, "end": **********.701354, "relative_end": **********.701354, "duration": 0.*****************, "duration_str": "8.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.802072, "relative_start": 1.****************, "end": **********.802815, "relative_end": **********.802815, "duration": 0.0007429122924804688, "duration_str": "743μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.80398, "relative_start": 1.****************, "end": **********.804028, "relative_end": **********.804028, "duration": 4.792213439941406e-05, "duration_str": "48μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "61MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.14.1", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009649999999999999, "accumulated_duration_str": "9.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '20' limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.716577, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "money_chain", "explain": null, "start_percent": 0, "width_percent": 5.907}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.729655, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "money_chain", "explain": null, "start_percent": 5.907, "width_percent": 9.637}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-28 14:37:01', `personal_access_tokens`.`updated_at` = '2025-08-28 14:37:01' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-08-28 14:37:01", "2025-08-28 14:37:01", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Middleware\\Authenticate.php", "line": 32}], "start": **********.735012, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php:83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "money_chain", "explain": null, "start_percent": 15.544, "width_percent": 54.611}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.748751, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "WalletController.php:24", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/WalletController.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Controllers\\Api\\WalletController.php", "line": 24}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:24", "ajax": false, "filename": "WalletController.php", "line": "24"}, "connection": "money_chain", "explain": null, "start_percent": 70.155, "width_percent": 6.321}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/WalletResource.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Resources\\WalletResource.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 107}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 254}, {"index": 30, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.78967, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php:139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "money_chain", "explain": null, "start_percent": 76.477, "width_percent": 6.943}, {"sql": "select * from `currencies` where `currencies`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/WalletResource.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Resources\\WalletResource.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 107}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 254}, {"index": 30, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.796108, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php:139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "money_chain", "explain": null, "start_percent": 83.42, "width_percent": 6.218}, {"sql": "select * from `currencies` where `currencies`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/WalletResource.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Resources\\WalletResource.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 107}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 254}, {"index": 30, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.798149, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php:139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "money_chain", "explain": null, "start_percent": 89.637, "width_percent": 3.523}, {"sql": "select * from `currencies` where `currencies`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/WalletResource.php", "file": "C:\\laragon\\www\\money-chain\\app\\Http\\Resources\\WalletResource.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 107}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 254}, {"index": 30, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.800091, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\laragon\\www\\money-chain\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php:139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "money_chain", "explain": null, "start_percent": 93.161, "width_percent": 6.839}]}, "models": {"data": {"App\\Models\\UserWallet": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php:1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index", "uri": "GET api/user/wallets", "controller": "App\\Http\\Controllers\\Api\\WalletController@index<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/user", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fmoney-chain%2Fapp%2FHttp%2FControllers%2FApi%2FWalletController.php:21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/WalletController.php:21-29</a>", "middleware": "api, auth:sanctum, auth:sanctum", "telescope": "<a href=\"https://money-chain.test/_debugbar/telescope/9fbdae51-0721-41c9-85d9-a6d7e3464dbd\" target=\"_blank\">View in Telescope</a>", "duration": "1.12s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1147845689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1147845689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1260484882 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1260484882\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-436349607 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">money-chain.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postman-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">02de0308-1dcd-4d4f-820d-1309e17eaf2e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">PostmanRuntime/7.45.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 20|OW******</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436349607\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-942938326 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>money_chain_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942938326\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1497869669 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 08:37:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"145 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28 Aug 2025 10:37:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">money_chain_session=H2LM0704WoW6v8seUKGkOHqvCGucFAWYcYLivvYD; expires=Thu, 28-Aug-2025 10:37:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497869669\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2145472592 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16mhBB7JACIDVcdAs4EiKZWlIdLcdd5zuOriLhmu</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://money-chain.test/api/user/wallets</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145472592\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://money-chain.test/api/user/wallets", "action_name": "wallets.index", "controller_action": "App\\Http\\Controllers\\Api\\WalletController@index"}, "badge": null}}