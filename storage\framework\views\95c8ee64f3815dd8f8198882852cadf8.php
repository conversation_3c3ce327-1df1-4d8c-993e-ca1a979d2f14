<?php if(session('notify')): ?>
    <?php
        $notification = session('notify');
        $icon = $notification['type'] == 'success' ? 'check' : 'alert-triangle';
    ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        'notification-popup',
        'popup-success' => $notification['type'] == 'success',
        'popup-danger' => $notification['type'] != 'success',
    ]); ?>">
        <div class="icon">
            <?php if($notification['type'] == 'success'): ?>
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M17.7221 4.29776C18.2103 4.78591 18.2103 5.57737 17.7221 6.06552L8.55543 15.2322C8.06728 15.7204 7.27582 15.7204 6.78767 15.2322L2.621 11.0655C2.13284 10.5774 2.13284 9.78591 2.621 9.29776C3.10915 8.8096 3.90061 8.8096 4.38877 9.29776L7.67155 12.5805L15.9543 4.29776C16.4425 3.8096 17.2339 3.8096 17.7221 4.29776Z"
                        fill="white"></path>
                </svg>
            <?php else: ?>
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12.5" r="12" fill="#F21B1B"></circle>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M15.8248 8.67557C16.096 8.94676 16.096 9.38646 15.8248 9.65766L9.15815 16.3243C8.88695 16.5955 8.44725 16.5955 8.17605 16.3243C7.90486 16.0531 7.90486 15.6134 8.17605 15.3422L14.8427 8.67557C15.1139 8.40437 15.5536 8.40437 15.8248 8.67557Z"
                        fill="white"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8.17605 8.67557C8.44725 8.40437 8.88695 8.40437 9.15815 8.67557L15.8248 15.3422C16.096 15.6134 16.096 16.0531 15.8248 16.3243C15.5536 16.5955 15.1139 16.5955 14.8427 16.3243L8.17605 9.65766C7.90486 9.38646 7.90486 8.94676 8.17605 8.67557Z"
                        fill="white"></path>
                </svg>
            <?php endif; ?>
        </div>
        <div class="content">
            <p class="title"><?php echo e(ucfirst($notification['title'])); ?></p>
            <p class="description"><?php echo e($notification['message']); ?></p>
        </div>
        <button class="close-btn">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M18.8839 5.11612C19.372 5.60427 19.372 6.39573 18.8839 6.88388L6.88388 18.8839C6.39573 19.372 5.60427 19.372 5.11612 18.8839C4.62796 18.3957 4.62796 17.6043 5.11612 17.1161L17.1161 5.11612C17.6043 4.62796 18.3957 4.62796 18.8839 5.11612Z"
                    fill="#ACACB0"></path>
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M5.11612 5.11612C5.60427 4.62796 6.39573 4.62796 6.88388 5.11612L18.8839 17.1161C19.372 17.6043 19.372 18.3957 18.8839 18.8839C18.3957 19.372 17.6043 19.372 17.1161 18.8839L5.11612 6.88388C4.62796 6.39573 4.62796 5.60427 5.11612 5.11612Z"
                    fill="#ACACB0"></path>
            </svg>
        </button>
    </div>
<?php endif; ?>
<?php if($errors->any()): ?>
    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'notification-popup',
            'popup-danger',
        ]); ?>">
            <div class="icon">
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12.5" r="12" fill="#F21B1B"></circle>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M15.8248 8.67557C16.096 8.94676 16.096 9.38646 15.8248 9.65766L9.15815 16.3243C8.88695 16.5955 8.44725 16.5955 8.17605 16.3243C7.90486 16.0531 7.90486 15.6134 8.17605 15.3422L14.8427 8.67557C15.1139 8.40437 15.5536 8.40437 15.8248 8.67557Z"
                        fill="white"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8.17605 8.67557C8.44725 8.40437 8.88695 8.40437 9.15815 8.67557L15.8248 15.3422C16.096 15.6134 16.096 16.0531 15.8248 16.3243C15.5536 16.5955 15.1139 16.5955 14.8427 16.3243L8.17605 9.65766C7.90486 9.38646 7.90486 8.94676 8.17605 8.67557Z"
                        fill="white"></path>
                </svg>
            </div>
            <div class="content">
                <p class="title"><?php echo e(__('Validation Error')); ?></p>
                <p class="description"><?php echo e($error); ?></p>
            </div>
            <button class="close-btn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M18.8839 5.11612C19.372 5.60427 19.372 6.39573 18.8839 6.88388L6.88388 18.8839C6.39573 19.372 5.60427 19.372 5.11612 18.8839C4.62796 18.3957 4.62796 17.6043 5.11612 17.1161L17.1161 5.11612C17.6043 4.62796 18.3957 4.62796 18.8839 5.11612Z"
                        fill="#ACACB0"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M5.11612 5.11612C5.60427 4.62796 6.39573 4.62796 6.88388 5.11612L18.8839 17.1161C19.372 17.6043 19.372 18.3957 18.8839 18.8839C18.3957 19.372 17.6043 19.372 17.1161 18.8839L5.11612 6.88388C4.62796 6.39573 4.62796 5.60427 5.11612 5.11612Z"
                        fill="#ACACB0"></path>
                </svg>
            </button>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\money-chain\app\Providers/../../resources/views/frontend/default/include/_notify.blade.php ENDPATH**/ ?>