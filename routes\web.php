<?php

use App\Http\Controllers\AppController;
use App\Http\Controllers\CronJobController;
use App\Http\Controllers\Frontend\AddMoneyController;
use App\Http\Controllers\Frontend\DashboardController;
use App\Http\Controllers\Frontend\ExchangeController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\KycController;
use App\Http\Controllers\Frontend\PageController;
use App\Http\Controllers\Frontend\SettingController;
use App\Http\Controllers\Frontend\TicketController;
use App\Http\Controllers\Frontend\TransactionController;
use App\Http\Controllers\Frontend\TransferMoneyController;
use App\Http\Controllers\Frontend\User\CashoutController;
use App\Http\Controllers\Frontend\User\GiftController;
use App\Http\Controllers\Frontend\User\InvoiceController;
use App\Http\Controllers\Frontend\User\PaymentController;
use App\Http\Controllers\Frontend\User\RequestMoneyController;
use App\Http\Controllers\Frontend\UserController;
use App\Http\Controllers\Frontend\UserReferralController;
use App\Http\Controllers\Frontend\UserWalletController;
use App\Http\Controllers\Frontend\WithdrawAccountController;
use App\Http\Controllers\Frontend\WithdrawMoneyController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\StatusController;
use Illuminate\Container\Attributes\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Landing Pages
Route::controller(HomeController::class)->group(function () {
    Route::get('/', 'home')->name('home');
    Route::get('blog/{id}', 'blogDetails')->name('blog.details');
    Route::get('api-documentation', 'documentation')->name('api.documentation');
});

// Contact Form
Route::post('mail-send', [PageController::class, 'mailSend'])->name('mail-send');
Route::post('subscriber', [HomeController::class, 'subscribeNow'])->name('subscriber');

// Gateway status
Route::group(['controller' => StatusController::class, 'prefix' => 'status', 'as' => 'status.'], function () {
    Route::match(['get', 'post'], '/success', 'success')->name('success');
    Route::match(['get', 'post'], '/cancel', 'cancel')->name('cancel');
    Route::match(['get', 'post'], '/pending', 'pending')->name('pending');
});

// Translate
Route::get('language-update/{locale}', [HomeController::class, 'languageUpdate'])->name('language-update');

// Dynamic Page
Route::get('page/{section}', [PageController::class, 'getPage'])->name('dynamic.page');

// Without auth
Route::get('notification-tune', [AppController::class, 'notificationTune'])->name('notification-tune');

// Site cron job
Route::get('site-cron', [CronJobController::class, 'runCronJobs'])->name('cron.job');

// Auth
require __DIR__.'/auth.php';
